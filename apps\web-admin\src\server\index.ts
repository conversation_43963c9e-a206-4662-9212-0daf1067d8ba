/**
 * @file apps/web-admin/src/server/index.ts
 * @description Server-side exports for the Web Admin App
 * 
 * This file serves as the main entry point for all server-side functionality
 * specific to the admin application. It re-exports server actions, utilities,
 * and services that are used throughout the admin app.
 */

import 'server-only';

// ========================================
// ADMIN SERVER ACTIONS
// ========================================

// User management server actions
export {
  createUser,
  updateUser,
  deleteUser,
  getUserById,
  getUsersList,
  banUser,
  unbanUser,
  type UserManagementData,
  type UserListFilters,
} from './actions/user-management-actions';

// Admin operations server actions
export {
  createAdminUser,
  updateAdminUser,
  deleteAdminUser,
  assignAdminRole,
  revokeAdminRole,
  validateAdminPermissions,
  type AdminUserData,
  type AdminRoleAssignment,
} from './actions/admin-actions';

// Audit logging server actions
export {
  logAdminAction,
  getAuditLogs,
  exportAuditLogs,
  type AuditLogEntry,
  type AuditLogFilters,
} from './actions/audit-actions';

// Dashboard analytics server actions
export {
  getDashboardStats,
  getUserAnalytics,
  getSystemMetrics,
  generateReport,
  type DashboardStats,
  type AnalyticsFilters,
} from './actions/dashboard-actions';

// ========================================
// ADMIN SERVER UTILITIES
// ========================================

// Admin-specific server utilities
export {
  validateAdminRequest,
  checkAdminPermissions,
  sanitizeAdminInput,
  formatAdminResponse,
  handleAdminError,
} from './utils/admin-utils';

// Permission management utilities
export {
  hasPermission,
  checkResourceAccess,
  validateRolePermissions,
  type Permission,
  type ResourceAccess,
} from './utils/permissions';

// Data validation utilities
export {
  validateUserData,
  validateAdminData,
  validateSystemSettings,
  type ValidationResult,
} from './utils/validation';

// ========================================
// ADMIN SERVER UTILITIES
// ========================================

// Data export utilities (admin-specific)
export {
  generateUserDataExport,
  generateAuditLogExport,
  formatExportData,
  type AdminExportOptions,
} from './utils/export-utils';

// ========================================
// TYPE EXPORTS
// ========================================

// Re-export common admin server types
export type {
  AdminServerActionResult,
  AdminServerError,
  AdminValidationError,
  AdminRequestContext,
} from './types/admin-server-types';
