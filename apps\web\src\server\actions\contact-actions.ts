/**
 * @file apps/web/src/server/actions/contact-actions.ts
 * @description Server actions for handling contact form submissions
 */

'use server';

import 'server-only';
import { z } from 'zod';
import { sendToTopic } from '@encreasl/fcm/server';
import type { ServerActionResult, ValidationError } from '../types/server-types';
import { validateServerRequest, handleServerError } from '../utils/server-utils';
import { checkRateLimit } from '../utils/rate-limit';

// ========================================
// VALIDATION SCHEMAS
// ========================================

const ContactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name too long'),
  email: z.string().email('Invalid email address'),
  company: z.string().optional(),
  phone: z.string().optional(),
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(200, 'Subject too long'),
  message: z.string().min(10, 'Message must be at least 10 characters').max(2000, 'Message too long'),
  source: z.string().optional(), // Track where the form was submitted from
});

export type ContactFormData = z.infer<typeof ContactFormSchema>;

export type ContactFormSubmission = {
  id: string;
  data: ContactFormData;
  submittedAt: string;
  ipAddress?: string;
  userAgent?: string;
};

// ========================================
// SERVER ACTIONS
// ========================================

/**
 * Submit contact form data
 */
export async function submitContactForm(
  formData: ContactFormData
): Promise<ServerActionResult<ContactFormSubmission>> {
  try {
    // Rate limiting check
    const rateLimitResult = await checkRateLimit('contact-form', {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 3, // Max 3 submissions per 15 minutes
    });

    if (!rateLimitResult.allowed) {
      return {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many contact form submissions. Please try again later.',
          timestamp: new Date().toISOString(),
        },
      };
    }

    // Validate form data
    const validationResult = validateContactFormData(formData);
    if (!validationResult.success) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid form data',
          details: { errors: validationResult.errors },
          timestamp: new Date().toISOString(),
        },
      };
    }

    // Get request context
    const context = await validateServerRequest();
    
    // Create submission record
    const submission: ContactFormSubmission = {
      id: crypto.randomUUID(),
      data: validationResult.data,
      submittedAt: new Date().toISOString(),
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    };

    // Save to Firestore (this will trigger Firebase Function for email sending)
    // In a real implementation, you would save to your database here
    // The Firebase Function will automatically handle email notifications
    console.log('Contact form submission:', submission);

    // Send FCM notification to admin
    await sendToTopic('contact_submissions', {
      title: 'New Contact Form Submission',
      body: `${submission.data.name} submitted a contact form`,
      data: {
        type: 'contact_form',
        submissionId: submission.id,
        name: submission.data.name,
        email: submission.data.email,
      },
    });

    return {
      success: true,
      data: submission,
      message: 'Contact form submitted successfully',
    };

  } catch (error) {
    return handleServerError(error, 'CONTACT_FORM_SUBMISSION_ERROR');
  }
}

/**
 * Validate contact form data
 */
export function validateContactFormData(data: unknown): {
  success: boolean;
  data?: ContactFormData;
  errors?: ValidationError[];
} {
  try {
    const validatedData = ContactFormSchema.parse(data);
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: ValidationError[] = error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      }));
      
      return {
        success: false,
        errors,
      };
    }
    
    return {
      success: false,
      errors: [{
        field: 'unknown',
        message: 'Validation failed',
        code: 'UNKNOWN_ERROR',
      }],
    };
  }
}
