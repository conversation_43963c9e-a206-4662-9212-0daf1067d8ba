/**
 * @file apps/web-admin/src/server/actions/admin-actions.ts
 * @description Server actions for admin user management
 */

'use server';

import 'server-only';
import { z } from 'zod';
import { verifyAdminUser, setCustomUserClaims } from '@encreasl/auth/server';
import { sendToTopic } from '@encreasl/fcm/server';
import type { 
  AdminServerActionResult, 
  AdminUserData,
  AdminRequestContext 
} from '../types/admin-server-types';
import { validateAdminRequest, handleAdminError } from '../utils/admin-utils';
import { hasPermission } from '../utils/permissions';
import { logAdminAction } from './audit-actions';

// ========================================
// VALIDATION SCHEMAS
// ========================================

const AdminUserDataSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  roles: z.array(z.string()).min(1, 'At least one role is required'),
  permissions: z.array(z.string()).default([]),
  status: z.enum(['active', 'inactive', 'suspended']).default('active'),
});

const RoleAssignmentSchema = z.object({
  adminId: z.string().min(1, 'Admin ID is required'),
  roles: z.array(z.string()).min(1, 'At least one role is required'),
  permissions: z.array(z.string()).default([]),
});

export type AdminRoleAssignment = z.infer<typeof RoleAssignmentSchema>;

// ========================================
// ADMIN MANAGEMENT ACTIONS
// ========================================

/**
 * Create a new admin user
 */
export async function createAdminUser(
  adminData: Omit<AdminUserData, 'id' | 'createdAt' | 'updatedAt' | 'lastLoginAt'>
): Promise<AdminServerActionResult<AdminUserData>> {
  try {
    // Validate super admin permissions
    const currentAdmin = await verifyAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'admins', 'create')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to create admin users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'high',
        },
      };
    }

    // Validate admin data
    const validatedData = AdminUserDataSchema.parse(adminData);
    
    // Create admin user record
    const newAdmin: AdminUserData = {
      id: crypto.randomUUID(),
      ...validatedData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Set custom claims for the new admin
    await setCustomUserClaims(newAdmin.id!, {
      admin: true,
      roles: newAdmin.roles,
      permissions: newAdmin.permissions,
    });

    // Here you would typically save to your database
    console.log('Creating admin user:', newAdmin);

    // Log admin action
    await logAdminAction({
      action: 'admin_created',
      resource: 'admin',
      resourceId: newAdmin.id,
      details: {
        adminEmail: newAdmin.email,
        roles: newAdmin.roles,
        permissions: newAdmin.permissions,
      },
    });

    // Send critical notification
    await sendToTopic('admin_notifications', {
      title: 'New Admin Created',
      body: `New admin ${newAdmin.email} was created by ${currentAdmin.email}`,
      data: {
        type: 'admin_created',
        adminId: newAdmin.id!,
        createdBy: context.adminId,
        severity: 'high',
      },
    });

    return {
      success: true,
      data: newAdmin,
      message: 'Admin user created successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'admin_created',
        resourceId: newAdmin.id,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'ADMIN_CREATION_ERROR');
  }
}

/**
 * Update an existing admin user
 */
export async function updateAdminUser(
  adminId: string,
  adminData: Partial<Omit<AdminUserData, 'id' | 'createdAt' | 'updatedAt' | 'lastLoginAt'>>
): Promise<AdminServerActionResult<AdminUserData>> {
  try {
    // Validate super admin permissions
    const currentAdmin = await verifyAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'admins', 'update')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to update admin users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'high',
        },
      };
    }

    // Prevent self-modification of critical fields
    if (adminId === context.adminId && (adminData.roles || adminData.status)) {
      return {
        success: false,
        error: {
          code: 'SELF_MODIFICATION_DENIED',
          message: 'You cannot modify your own roles or status',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'medium',
        },
      };
    }

    // Validate admin data (partial)
    const validatedData = AdminUserDataSchema.partial().parse(adminData);
    
    // Update admin user record
    const updatedAdmin: AdminUserData = {
      id: adminId,
      email: '<EMAIL>', // This would come from database
      firstName: 'Admin',
      lastName: 'User',
      roles: ['admin'],
      permissions: [],
      status: 'active',
      ...validatedData,
      updatedAt: new Date().toISOString(),
    };

    // Update custom claims if roles or permissions changed
    if (validatedData.roles || validatedData.permissions) {
      await setCustomUserClaims(adminId, {
        admin: true,
        roles: updatedAdmin.roles,
        permissions: updatedAdmin.permissions,
      });
    }

    // Here you would typically update in your database
    console.log('Updating admin user:', updatedAdmin);

    // Log admin action
    await logAdminAction({
      action: 'admin_updated',
      resource: 'admin',
      resourceId: adminId,
      details: {
        updatedFields: Object.keys(validatedData),
        changes: validatedData,
      },
    });

    return {
      success: true,
      data: updatedAdmin,
      message: 'Admin user updated successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'admin_updated',
        resourceId: adminId,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'ADMIN_UPDATE_ERROR');
  }
}

/**
 * Delete an admin user
 */
export async function deleteAdminUser(
  adminId: string
): Promise<AdminServerActionResult<{ adminId: string; deletedAt: string }>> {
  try {
    // Validate super admin permissions
    const currentAdmin = await verifyAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'admins', 'delete')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to delete admin users',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'critical',
        },
      };
    }

    // Prevent self-deletion
    if (adminId === context.adminId) {
      return {
        success: false,
        error: {
          code: 'SELF_DELETION_DENIED',
          message: 'You cannot delete your own admin account',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'high',
        },
      };
    }

    // Remove custom claims
    await setCustomUserClaims(adminId, {
      admin: null,
      roles: null,
      permissions: null,
    });

    // Here you would typically delete from your database
    console.log('Deleting admin user:', adminId);

    const deletionResult = {
      adminId,
      deletedAt: new Date().toISOString(),
    };

    // Log admin action
    await logAdminAction({
      action: 'admin_deleted',
      resource: 'admin',
      resourceId: adminId,
      details: {
        deletedAt: deletionResult.deletedAt,
      },
    });

    // Send critical notification
    await sendToTopic('admin_notifications', {
      title: 'Admin Deleted',
      body: `Admin ${adminId} was deleted by ${currentAdmin.email}`,
      data: {
        type: 'admin_deleted',
        adminId,
        deletedBy: context.adminId,
        severity: 'critical',
      },
    });

    return {
      success: true,
      data: deletionResult,
      message: 'Admin user deleted successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'admin_deleted',
        resourceId: adminId,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'ADMIN_DELETION_ERROR');
  }
}

/**
 * Assign role to admin user
 */
export async function assignAdminRole(
  roleAssignment: AdminRoleAssignment
): Promise<AdminServerActionResult<AdminUserData>> {
  try {
    // Validate super admin permissions
    const currentAdmin = await verifyAdminUser();
    const context = await validateAdminRequest();

    if (!hasPermission(context.permissions, 'admins', 'assign_roles')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to assign admin roles',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'high',
        },
      };
    }

    // Validate role assignment data
    const validatedData = RoleAssignmentSchema.parse(roleAssignment);

    // Update admin user with new roles
    const updatedAdmin: AdminUserData = {
      id: validatedData.adminId,
      email: '<EMAIL>', // This would come from database
      firstName: 'Admin',
      lastName: 'User',
      roles: validatedData.roles,
      permissions: validatedData.permissions,
      status: 'active',
      updatedAt: new Date().toISOString(),
    };

    // Update custom claims
    await setCustomUserClaims(validatedData.adminId, {
      admin: true,
      roles: validatedData.roles,
      permissions: validatedData.permissions,
    });

    // Log admin action
    await logAdminAction({
      action: 'admin_role_assigned',
      resource: 'admin',
      resourceId: validatedData.adminId,
      details: {
        roles: validatedData.roles,
        permissions: validatedData.permissions,
      },
    });

    return {
      success: true,
      data: updatedAdmin,
      message: 'Admin roles assigned successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'admin_role_assigned',
        resourceId: validatedData.adminId,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'ADMIN_ROLE_ASSIGNMENT_ERROR');
  }
}

/**
 * Revoke role from admin user
 */
export async function revokeAdminRole(
  adminId: string,
  rolesToRevoke: string[]
): Promise<AdminServerActionResult<AdminUserData>> {
  try {
    // Validate super admin permissions
    const currentAdmin = await verifyAdminUser();
    const context = await validateAdminRequest();

    if (!hasPermission(context.permissions, 'admins', 'revoke_roles')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to revoke admin roles',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'high',
        },
      };
    }

    // Prevent self-role revocation
    if (adminId === context.adminId) {
      return {
        success: false,
        error: {
          code: 'SELF_ROLE_REVOCATION_DENIED',
          message: 'You cannot revoke your own admin roles',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'medium',
        },
      };
    }

    // Here you would fetch current admin data and remove specified roles
    const updatedAdmin: AdminUserData = {
      id: adminId,
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      roles: ['admin'], // This would be filtered to remove revoked roles
      permissions: [],
      status: 'active',
      updatedAt: new Date().toISOString(),
    };

    // Update custom claims
    await setCustomUserClaims(adminId, {
      admin: true,
      roles: updatedAdmin.roles,
      permissions: updatedAdmin.permissions,
    });

    // Log admin action
    await logAdminAction({
      action: 'admin_role_revoked',
      resource: 'admin',
      resourceId: adminId,
      details: {
        revokedRoles: rolesToRevoke,
        remainingRoles: updatedAdmin.roles,
      },
    });

    return {
      success: true,
      data: updatedAdmin,
      message: 'Admin roles revoked successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'admin_role_revoked',
        resourceId: adminId,
      },
    };

  } catch (error) {
    return handleAdminError(error, 'ADMIN_ROLE_REVOCATION_ERROR');
  }
}

/**
 * Validate admin permissions for a specific action
 */
export async function validateAdminPermissions(
  resource: string,
  action: string
): Promise<AdminServerActionResult<{ hasPermission: boolean; permissions: string[] }>> {
  try {
    // Validate admin user
    await verifyAdminUser();
    const context = await validateAdminRequest();

    const hasRequiredPermission = hasPermission(context.permissions, resource, action);

    return {
      success: true,
      data: {
        hasPermission: hasRequiredPermission,
        permissions: context.permissions,
      },
      message: hasRequiredPermission ? 'Permission granted' : 'Permission denied',
    };

  } catch (error) {
    return handleAdminError(error, 'PERMISSION_VALIDATION_ERROR');
  }
}
