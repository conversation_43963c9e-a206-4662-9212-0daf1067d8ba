/**
 * @file apps/web-admin/src/server/actions/audit-actions.ts
 * @description Server actions for audit logging and retrieval
 */

'use server';

import 'server-only';
import { z } from 'zod';
import { verifyAdminUser } from '@encreasl/auth/server';
import type { 
  AdminServerActionResult, 
  AuditLogEntry,
  AnalyticsFilters 
} from '../types/admin-server-types';
import { validateAdminRequest, handleAdminError } from '../utils/admin-utils';
import { hasPermission } from '../utils/permissions';

// ========================================
// VALIDATION SCHEMAS
// ========================================

const AuditLogEntrySchema = z.object({
  action: z.string().min(1, 'Action is required'),
  resource: z.string().min(1, 'Resource is required'),
  resourceId: z.string().optional(),
  details: z.record(z.unknown()).default({}),
  severity: z.enum(['info', 'warning', 'error']).default('info'),
});

const AuditLogFiltersSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  adminId: z.string().optional(),
  action: z.string().optional(),
  resource: z.string().optional(),
  severity: z.enum(['info', 'warning', 'error']).optional(),
  limit: z.number().min(1).max(1000).default(100),
  offset: z.number().min(0).default(0),
});

export type AuditLogFilters = z.infer<typeof AuditLogFiltersSchema>;

// ========================================
// AUDIT LOGGING ACTIONS
// ========================================

/**
 * Log an admin action
 */
export async function logAdminAction(
  logData: Omit<AuditLogEntry, 'id' | 'adminId' | 'adminEmail' | 'ipAddress' | 'userAgent' | 'timestamp'>
): Promise<AdminServerActionResult<AuditLogEntry>> {
  try {
    // Get admin context
    const adminUser = await verifyAdminUser();
    const context = await validateAdminRequest();
    
    // Validate log data
    const validatedData = AuditLogEntrySchema.parse(logData);
    
    // Create audit log entry
    const auditEntry: AuditLogEntry = {
      id: crypto.randomUUID(),
      adminId: context.adminId,
      adminEmail: adminUser.email || 'unknown',
      ...validatedData,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      timestamp: new Date().toISOString(),
    };

    // Here you would typically save to your audit log database
    console.log('Audit log entry:', auditEntry);

    return {
      success: true,
      data: auditEntry,
      message: 'Action logged successfully',
    };

  } catch (error) {
    return handleAdminError(error, 'AUDIT_LOG_ERROR');
  }
}

/**
 * Get audit logs with filters
 */
export async function getAuditLogs(
  filters: AuditLogFilters = {}
): Promise<AdminServerActionResult<{
  logs: AuditLogEntry[];
  total: number;
  hasMore: boolean;
}>> {
  try {
    // Validate admin permissions
    await verifyAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'audit_logs', 'read')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to view audit logs',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'medium',
        },
      };
    }

    // Validate filters
    const validatedFilters = AuditLogFiltersSchema.parse(filters);

    // Here you would typically query your audit log database
    const mockLogs: AuditLogEntry[] = [
      {
        id: '1',
        adminId: context.adminId,
        adminEmail: '<EMAIL>',
        action: 'user_created',
        resource: 'user',
        resourceId: 'user123',
        details: { userEmail: '<EMAIL>' },
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0...',
        timestamp: new Date().toISOString(),
        severity: 'info',
      },
      {
        id: '2',
        adminId: context.adminId,
        adminEmail: '<EMAIL>',
        action: 'user_deleted',
        resource: 'user',
        resourceId: 'user456',
        details: { userEmail: '<EMAIL>' },
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0...',
        timestamp: new Date().toISOString(),
        severity: 'warning',
      },
    ];

    const result = {
      logs: mockLogs,
      total: mockLogs.length,
      hasMore: false,
    };

    return {
      success: true,
      data: result,
      message: 'Audit logs retrieved successfully',
    };

  } catch (error) {
    return handleAdminError(error, 'AUDIT_LOGS_RETRIEVAL_ERROR');
  }
}

/**
 * Export audit logs
 */
export async function exportAuditLogs(
  filters: AuditLogFilters = {},
  format: 'csv' | 'json' = 'csv'
): Promise<AdminServerActionResult<{
  downloadUrl: string;
  filename: string;
  expiresAt: string;
}>> {
  try {
    // Validate admin permissions
    await verifyAdminUser();
    const context = await validateAdminRequest();
    
    if (!hasPermission(context.permissions, 'audit_logs', 'export')) {
      return {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'You do not have permission to export audit logs',
          timestamp: new Date().toISOString(),
          adminId: context.adminId,
          severity: 'medium',
        },
      };
    }

    // Validate filters
    const validatedFilters = AuditLogFiltersSchema.parse(filters);

    // Use export utility to generate the export
    const mockLogs: AuditLogEntry[] = []; // This would come from database query
    const exportResult = await import('../utils/export-utils').then(utils =>
      utils.generateAuditLogExport(mockLogs, {
        format,
        filters: validatedFilters,
        includeMetadata: true,
      })
    );

    console.log('Exporting audit logs:', { filters: validatedFilters, format, exportResult });

    // Log the export action
    await logAdminAction({
      action: 'audit_logs_exported',
      resource: 'audit_logs',
      details: {
        format,
        filters: validatedFilters,
        filename,
      },
      severity: 'info',
    });

    const result = exportResult;

    return {
      success: true,
      data: result,
      message: 'Audit logs export prepared successfully',
      metadata: {
        timestamp: new Date().toISOString(),
        adminId: context.adminId,
        action: 'audit_logs_exported',
      },
    };

  } catch (error) {
    return handleAdminError(error, 'AUDIT_LOGS_EXPORT_ERROR');
  }
}
