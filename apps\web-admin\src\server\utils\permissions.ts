/**
 * @file apps/web-admin/src/server/utils/permissions.ts
 * @description Permission management utilities for admin operations
 */

import 'server-only';
import type { Permission, ResourceAccess } from '../types/admin-server-types';

// ========================================
// PERMISSION DEFINITIONS
// ========================================

/**
 * Available permissions in the system
 */
export const PERMISSIONS: Record<string, Permission> = {
  // User management permissions
  'users.create': {
    id: 'users.create',
    name: 'Create Users',
    description: 'Create new user accounts',
    resource: 'users',
    action: 'create',
  },
  'users.read': {
    id: 'users.read',
    name: 'View Users',
    description: 'View user accounts and details',
    resource: 'users',
    action: 'read',
  },
  'users.update': {
    id: 'users.update',
    name: 'Update Users',
    description: 'Update user account information',
    resource: 'users',
    action: 'update',
  },
  'users.delete': {
    id: 'users.delete',
    name: 'Delete Users',
    description: 'Delete user accounts',
    resource: 'users',
    action: 'delete',
  },
  'users.ban': {
    id: 'users.ban',
    name: 'Ban Users',
    description: 'Ban user accounts',
    resource: 'users',
    action: 'ban',
  },
  'users.unban': {
    id: 'users.unban',
    name: 'Unban Users',
    description: 'Unban user accounts',
    resource: 'users',
    action: 'unban',
  },

  // Admin management permissions
  'admins.create': {
    id: 'admins.create',
    name: 'Create Admins',
    description: 'Create new admin accounts',
    resource: 'admins',
    action: 'create',
  },
  'admins.read': {
    id: 'admins.read',
    name: 'View Admins',
    description: 'View admin accounts and details',
    resource: 'admins',
    action: 'read',
  },
  'admins.update': {
    id: 'admins.update',
    name: 'Update Admins',
    description: 'Update admin account information',
    resource: 'admins',
    action: 'update',
  },
  'admins.delete': {
    id: 'admins.delete',
    name: 'Delete Admins',
    description: 'Delete admin accounts',
    resource: 'admins',
    action: 'delete',
  },
  'admins.assign_roles': {
    id: 'admins.assign_roles',
    name: 'Assign Admin Roles',
    description: 'Assign roles to admin accounts',
    resource: 'admins',
    action: 'assign_roles',
  },
  'admins.revoke_roles': {
    id: 'admins.revoke_roles',
    name: 'Revoke Admin Roles',
    description: 'Revoke roles from admin accounts',
    resource: 'admins',
    action: 'revoke_roles',
  },

  // Audit log permissions
  'audit_logs.read': {
    id: 'audit_logs.read',
    name: 'View Audit Logs',
    description: 'View system audit logs',
    resource: 'audit_logs',
    action: 'read',
  },
  'audit_logs.export': {
    id: 'audit_logs.export',
    name: 'Export Audit Logs',
    description: 'Export audit logs to files',
    resource: 'audit_logs',
    action: 'export',
  },

  // System permissions
  'system.read': {
    id: 'system.read',
    name: 'View System Info',
    description: 'View system information and metrics',
    resource: 'system',
    action: 'read',
  },
  'system.update': {
    id: 'system.update',
    name: 'Update System Settings',
    description: 'Update system configuration',
    resource: 'system',
    action: 'update',
  },

  // Notification permissions
  'notifications.send': {
    id: 'notifications.send',
    name: 'Send Notifications',
    description: 'Send notifications to users',
    resource: 'notifications',
    action: 'send',
  },

  // Report permissions
  'reports.generate': {
    id: 'reports.generate',
    name: 'Generate Reports',
    description: 'Generate system reports',
    resource: 'reports',
    action: 'generate',
  },
  'reports.export': {
    id: 'reports.export',
    name: 'Export Reports',
    description: 'Export reports to files',
    resource: 'reports',
    action: 'export',
  },
};

// ========================================
// ROLE DEFINITIONS
// ========================================

/**
 * Predefined admin roles with their permissions
 */
export const ADMIN_ROLES: Record<string, {
  name: string;
  description: string;
  permissions: string[];
}> = {
  super_admin: {
    name: 'Super Admin',
    description: 'Full system access with all permissions',
    permissions: Object.keys(PERMISSIONS),
  },
  admin: {
    name: 'Admin',
    description: 'Standard admin with user and content management',
    permissions: [
      'users.create',
      'users.read',
      'users.update',
      'users.ban',
      'users.unban',
      'audit_logs.read',
      'system.read',
      'notifications.send',
      'reports.generate',
    ],
  },
  moderator: {
    name: 'Moderator',
    description: 'Content moderation and user management',
    permissions: [
      'users.read',
      'users.update',
      'users.ban',
      'users.unban',
      'audit_logs.read',
      'notifications.send',
    ],
  },
  support: {
    name: 'Support',
    description: 'Customer support with limited user access',
    permissions: [
      'users.read',
      'users.update',
      'audit_logs.read',
      'notifications.send',
    ],
  },
  viewer: {
    name: 'Viewer',
    description: 'Read-only access to system information',
    permissions: [
      'users.read',
      'audit_logs.read',
      'system.read',
      'reports.generate',
    ],
  },
};

// ========================================
// PERMISSION CHECKING FUNCTIONS
// ========================================

/**
 * Check if user has a specific permission
 */
export function hasPermission(
  userPermissions: string[],
  resource: string,
  action: string
): boolean {
  const permissionKey = `${resource}.${action}`;
  return userPermissions.includes(permissionKey);
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(
  userPermissions: string[],
  permissions: string[]
): boolean {
  return permissions.some(permission => userPermissions.includes(permission));
}

/**
 * Check if user has all of the specified permissions
 */
export function hasAllPermissions(
  userPermissions: string[],
  permissions: string[]
): boolean {
  return permissions.every(permission => userPermissions.includes(permission));
}

/**
 * Check resource access for a user
 */
export function checkResourceAccess(
  userPermissions: string[],
  resourceAccess: ResourceAccess
): boolean {
  return resourceAccess.actions.every(action => 
    hasPermission(userPermissions, resourceAccess.resource, action)
  );
}

/**
 * Get permissions for a role
 */
export function getRolePermissions(role: string): string[] {
  return ADMIN_ROLES[role]?.permissions || [];
}

/**
 * Get all permissions for multiple roles
 */
export function getPermissionsForRoles(roles: string[]): string[] {
  const allPermissions = new Set<string>();
  
  roles.forEach(role => {
    const rolePermissions = getRolePermissions(role);
    rolePermissions.forEach(permission => allPermissions.add(permission));
  });
  
  return Array.from(allPermissions);
}

/**
 * Validate role permissions
 */
export function validateRolePermissions(
  role: string,
  permissions: string[]
): { isValid: boolean; invalidPermissions: string[] } {
  const rolePermissions = getRolePermissions(role);
  const invalidPermissions = permissions.filter(
    permission => !rolePermissions.includes(permission)
  );
  
  return {
    isValid: invalidPermissions.length === 0,
    invalidPermissions,
  };
}

/**
 * Get permission details
 */
export function getPermissionDetails(permissionId: string): Permission | null {
  return PERMISSIONS[permissionId] || null;
}

/**
 * Get all available permissions
 */
export function getAllPermissions(): Permission[] {
  return Object.values(PERMISSIONS);
}

/**
 * Get permissions by resource
 */
export function getPermissionsByResource(resource: string): Permission[] {
  return Object.values(PERMISSIONS).filter(permission => 
    permission.resource === resource
  );
}

/**
 * Check if permission exists
 */
export function permissionExists(permissionId: string): boolean {
  return permissionId in PERMISSIONS;
}

/**
 * Check if role exists
 */
export function roleExists(role: string): boolean {
  return role in ADMIN_ROLES;
}
